module.exports = {
  pathPrefix: `/se-swe`,
  siteMetadata: {
    title: `Demografi`,
    description: `Sveriges demografiska utveckling`,
    keywords: [`gatsby`, `theme`, `react`],
    author: ``,
    siteUrl: `https://migrationskartan.se/`,
  },
  plugins: [
    `gatsby-plugin-image`,
    {
      resolve: `gatsby-source-filesystem`,
      options: {
        name: `images`,
        path: `${__dirname}/src/images`,
      },
    },
    `gatsby-transformer-sharp`,
    `gatsby-plugin-sharp`,
    {
      resolve: `gatsby-plugin-manifest`,
      options: {
        name: `gatsby-starter-default`,
        short_name: `starter`,
        start_url: `/`,
        background_color: `#ffffff`,
        // This will impact how browsers show your PWA/website
        // https://css-tricks.com/meta-theme-color-and-trickery/
        theme_color: `#cccccc`,
        display: `minimal-ui`,
        icon: `content/assets/catalyst-site-icon.png`, // This path is relative to the root of the site.
      },
    },
    `gatsby-plugin-react-leaflet`,
  ],
}
