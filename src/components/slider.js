import React from "react"
import Slider from "@mui/material/Slider"

const marks = []
for (let i = 2002; i < 2021; i = i + 2) {
  marks.push({ value: i, label: i.toString() })
}

function valuetext(value) {
  return `${value}°C`
}

class DiscreteSlider extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      internalValue: 2020,
    }
    this.handelChange = this.handelChange.bind(this)
  }

  handelChange(event, value) {
    this.setState({ internalValue: value })
  }

  render() {
    return (
      <div style={{ width: "95vw" }}>
        {/* <b>{this.state.internalValue}</b> */}
        <Slider
          defaultValue={2020}
          getAriaValueText={valuetext}
          aria-labelledby="discrete-slider-custom"
          step={1}
          valueLabelDisplay="auto"
          marks={marks}
          min={2002}
          max={2020}
          onChange={this.handelChange}
        />
      </div>
    )
  }
}

export default DiscreteSlider
