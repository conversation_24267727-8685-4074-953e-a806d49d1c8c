import React, {useEffect, useRef, useState} from 'react'
import Highcharts from 'highcharts'

export default function SimpleAreaChart(props) {
    const refContainer = useRef(null)
    // const [dataSeries, setDataSeries] = useState([])

    let series = []
    let rubrikkategori = ''
    useEffect(() => {
        if (props.category) {
            // console.log(props.category)
            switch (props.category) {
                case 'barn':
                    rubrikkategori = 'Barn'
                    series = [
                        {
                            name: 'Utländska barn',
                            color: 'red',
                            data: props.data.utländska_barn,
                        },
                        {
                            name: 'Svenska barn',
                            data: props.data.svenska_barn,
                        },
                    ]
                    break
                case 'pensionärer':
                    rubrikkategori = 'Pensionärer'
                    series = [
                        {
                            name: 'Utländska pensionärer',
                            color: 'red',

                            data: props.data.utländska_pensionärer,
                        },
                        {
                            name: 'Svenska pensionärer',
                            data: props.data.svenska_pensionärer,
                        },
                    ]
                    break
                case 'kvinnor_i_fertil_ålder':
                    rubrikkategori = '<PERSON>vinnor 14 – 44'
                    series = [
                        {
                            name: 'Utländska kvinnor 14 – 44',
                            color: 'red',
                            data: props.data.utländska_kvinnor_i_fertil_ålder,
                        },
                        {
                            name: 'Svenska kvinnor 14 – 44',
                            data: props.data.svenska_kvinnor_i_fertil_ålder,
                        },
                    ]
                    break
                case 'män_i_vapenför_ålder':
                    rubrikkategori = 'Män 14 – 44'
                    series = [
                        {
                            name: 'Utländska män 14 – 44',
                            color: 'red',
                            data: props.data.utländska_män_i_vapenför_ålder,
                        },
                        {
                            name: 'Svenska män 14 – 44',
                            data: props.data.svenska_män_i_vapenför_ålder,
                        },
                    ]
                    break
                default:
                    rubrikkategori = 'Alla kategorier'
                    series = [
                        {
                            name: 'Utländsk härkomst alla',
                            color: 'red',
                            data: props.data.utländska_alla,
                        },
                        {
                            name: 'Svensk härkomst alla',
                            data: props.data.svenska_alla,
                        },
                    ]
            }

            // setDataSeries(series)
        }
        Highcharts.chart(refContainer.current, {
            chart: {
                type: 'area',
                spacingBottom: 10,
                spacingTop: 10,
                spacingLeft: 10,
                spacingRight: 10,
            },
            title: {
                text: rubrikkategori + ' i ' + props.text,
            },
            subtitle: {
                text: 'Källa SCB',
            },
            xAxis: {
                categories: props.data.årtal,
                tickmarkPlacement: 'on',
                title: {
                    enabled: false,
                },
            },
            yAxis: {
                title: {
                    text: 'Antal',
                },
                labels: {
                    formatter: function () {
                        return this.value
                    },
                },
            },
            tooltip: {
                split: true,
                valueSuffix: '',
            },
            plotOptions: {
                area: {
                    stacking: 'normal',
                    lineColor: '#666666',
                    lineWidth: 1,
                    borderWidth: '500px',
                    marker: {
                        lineWidth: 1,
                        lineColor: '#666666',
                    },
                },
            },
            series: series,
        })
    }, [props])

    return (
        <div className="chartPopupBox" onClick={props.handleClose}>
            <div className="areaChart" ref={refContainer} />
        </div>
    )
}
