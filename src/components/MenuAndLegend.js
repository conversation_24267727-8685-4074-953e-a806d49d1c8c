import React from "react"

import MenuItem from "@mui/material/MenuItem"
import FormControl from "@mui/material/FormControl"
import { MenuList } from "@mui/material"
// import { withStyles } from "@mui/material/styles"
import Typography from "@mui/material/Typography"

import "./components.css"

// const StyledMenuItem = withStyles(theme => ({
//   root: {
//     "&:focus": {
//       backgroundColor: theme.palette.primary.main,
//       "& .MuiListItemIcon-root, & .MuiListItemText-primary": {
//         color: theme.palette.common.white,
//       },
//     },
//   },
// }))(MenuItem)

export default function MenuAndLegend(props) {
  // const classes = useStyles()
  const [selectedIndex, setSelectedIndex] = React.useState(-1)
  // const [itemNumber, setItemNumber] = React.useState(0)
  const handleListItemClick = (event, index, callback, category) => {
    setSelectedIndex(index)
    callback(category)
  }

  let itemNumber = 0

  const MenuOption = function statelessFunctionComponentClass(
    value,
    itemNumber
  ) {
    // setItemNumber(itemNumber + 1)
    // console.log('value ' + value + ' itemNumber ' + itemNumber)
    return (
      <MenuItem
        className="StyledMenuItem"
        id={value}
        selected={selectedIndex === itemNumber}
        onClick={event =>
          handleListItemClick(event, itemNumber, props.handleChange, value)
        }
      >
        {/* <ListItemIcon>
                <AllIcon />
            </ListItemIcon> */}
        <Typography
          variant="inherit"
          style={{ float: "right", marginLeft: "1vw" }}
        >
          {value}
        </Typography>
      </MenuItem>
    )
  }

  let CreateMenu = function statelessFunctionComponentClass(values) {
    return values.map(MenuOption)
  }

  const optioner = props.optioner

  return (
    <FormControl variant="filled" id="FormControl">
      <MenuList
        className="MenuList"
        id="demo-simple-select-filled"
        value={props.age}
      >
        <h3
          style={{
            marginTop: 0,
            marginBottom: 0,
            marginLeft: 5,
            textAlign: "center",
            color: "black",
          }}
        >
          {props.menuTitle}
        </h3>
        {CreateMenu(props.options)}
      </MenuList>
    </FormControl>
  )
}
