#FormControl {
  /* position: sticky; */
  /* width: 15vw; */
  margin-top: 0.5;
  /* margin-right: 0.5vw; */
  margin-bottom: 0;
  padding-bottom: 0;
  border: solid #999 1px;
  /* background-color: rgba(170, 168, 168, 0.171); */
}

#legendTop {
  /* position: absolute; */
  /* width: 15vw; */
  float: left;
  /* border: solid #999 1px; */
  /*background-color: rgba(170, 168, 168, 0.171); */
  color: rgb(0, 0, 0);
  margin-top: 0.5vh;
  /* border: solid rgb(255, 0, 0) 1px; */
  margin-right: 0.5vw;
  /* display: flex;
  flex-direction: column;
  align-items: center; */
  /* border: 1px solid red; */
}

.legend {
  margin-right: 1vw;
  margin-left: 0.5vw;
  padding-top: 0vh;
  line-height: 25px;
  color: rgb(0, 0, 0);
  /* border: solid #999 1px;
    background-color: rgba(170, 168, 168, 0.171); */

  /* border: 1px solid red; */
  /* z-index: 120; */
  float: right;
  zoom: 1.2;
  /* min-width: 300; */
  /* float: right; */
}

.legendTextArea {
  pointer-events: none;
  /* text-align: right;
    align-items: right; */
}

.legendTextArea div {
  white-space: nowrap;
  float: right;
}

.legendLines {
  width: max-content;
  /* word-wrap: break-word; */
  /* border: 2px solid rgb(255, 0, 0); */
}
.legendText {
  text-overflow: ellipsis;
}

.legend i {
  width: 18px;
  height: 18px;
  float: left;
  margin-right: 8px;
  opacity: 0.7;
  border: 2px solid rgb(0, 0, 0);
  top: 50%;
  position: relative;
  transform: translateY(25%);
}

#infoBoxPadding {
  padding-top: 30vh;
  /* border: 2px solid rgb(255, 0, 0); */
}

#infoBoxText {
  float: left;
  border: 2px solid rgb(0, 0, 0);
}
.MenuItem {
  /* border: 2px solid rgb(255, 0, 0); */
}

.MenuParent {
  /* border: 2px solid rgb(255, 0, 0); */
  border: solid #999 1px;
  background-color: rgba(170, 168, 168, 0.171);
}

@media (max-height: 599px) {
  .MenuParent {
    position: absolute;
    top: 0;
    right: 0;
    transform: scale(0.5);
    transform-origin: right top;
    display: flex;

    /* background-color: rgba(211, 211, 211, 0.521); */
    /* z-index: 130; */
  }
  #FormControl,
  .legend {
    display: inline-block;
    padding-left: 10;
    margin-right: 1vw;
  }
}
@media (min-height: 600px) {
  .MenuParent {
    position: absolute;
    top: 0;
    right: 0;
    transform: scale(0.75);
    transform-origin: right top;
    align-items: right;
    /* display: flex; */

    /* z-index: 150; */
  }
}

@media (max-width: 639px) {
  .MenuParent {
    z-index: 130;
  }
}
@media (min-width: 640px) {
  .MenuParent {
    z-index: 150;
  }
}

.MenuList {
  background-color: rgba(211, 211, 211, 0.521);
  /* min-width: 300; */
  color: black;
  /* width: 20vw; */
  /* margin-right: 1vw; */
  top: 0;
  right: 0;
}

.chartPopupBox {
  position: fixed;
  background: #adabab50;
  width: 100%;
  height: 100vh;
  top: 0;
  left: 0;
  z-index: 140;
  display: flex;
  flex-direction: column;
  align-items: center;
  column-gap: 0;
  row-gap: 0;
  justify-content: center;
}

.plotCaptionBox {
  background: #ffffff;
}

@media (max-width: 999px) {
  .areaChart {
    width: 70vw;
    height: 60vh;
    right: 0;
    margin-top: 0;
    margin-bottom: 0;
  }
  .chartCaptionText {
    min-width: 70vw;
    max-width: 70vw;
    background: #ffffff;
    margin-top: 0;
    margin-bottom: 0.5vh;
    padding-left: 10%;
    padding-right: 0%;
    font-size: 0.875em;
    white-space: pre-wrap;
    overflow-wrap: normal;
    color: #4d4d4d;
  }
}

@media (min-width: 1000px) {
  .areaChart {
    width: 55vw;
    height: 60vh;
    right: 0;
    /* border: 2px solid rgb(255, 0, 0); */
  }
  .chartCaptionText {
    min-width: 55vw;
    max-width: 55vw;
    background: #ffffff;
    margin-top: 0;
    margin-bottom: 0.5vh;
    padding-left: 10%;
    padding-right: 0%;
    font-size: 0.875em;
    white-space: pre-wrap;
    color: #4d4d4d;
  }
}

#sourceText {
  text-align: center;
  margin-bottom: 0;
}
#sourceText:hover {
  color: blue;
  text-align: center;
}
