import React from "react"
import MenuItem from "@mui/material/MenuItem"
import ListItemIcon from "@mui/material/ListItemIcon"
import { MaleIcon, WomanIcon, ChildIcon, RetiredIcon, AllIcon } from "./icons"
import FormControl from "@mui/material/FormControl"
import { MenuList } from "@mui/material"
import Typography from "@mui/material/Typography"
import SimpleLegend from "./simpleLegend"

import "./components.css"

export default function SimpleDemographicsMenu(props) {
  const [selectedIndex, setSelectedIndex] = React.useState(0)
  const handleListItemClick = (event, index, callback, category) => {
    setSelectedIndex(index)
    callback(category)
  }

  return (
    <FormControl variant="filled" id="FormControl">
      <MenuList
        className="MenuList"
        id="demo-simple-select-filled"
        value={props.age}
      >
        <h3
          style={{
            marginTop: 0,
            marginBottom: 0,
            marginLeft: 5,
            textAlign: "center",
            color: "black",
          }}
        >
          Ålderskategori
        </h3>
        <MenuItem
          className="StyledMenuItem"
          id={"alla"}
          selected={selectedIndex === 0}
          onClick={event =>
            handleListItemClick(event, 0, props.handleChange, "alla")
          }
        >
          <ListItemIcon>
            <AllIcon />
          </ListItemIcon>
          <Typography
            variant="inherit"
            style={{ float: "right", marginLeft: "1vw" }}
          >
            Alla
          </Typography>
        </MenuItem>

        <MenuItem
          className="StyledMenuItem"
          id={"barn"}
          selected={selectedIndex === 1}
          onClick={event =>
            handleListItemClick(event, 1, props.handleChange, "barn")
          }
        >
          <ListItemIcon>
            <ChildIcon />
          </ListItemIcon>
          <Typography variant="inherit">Barn under 14</Typography>
        </MenuItem>

        <MenuItem
          className="StyledMenuItem"
          id={"kvinnor_i_fertil_ålder"}
          selected={selectedIndex === 2}
          onClick={event =>
            handleListItemClick(
              event,
              2,
              props.handleChange,
              "kvinnor_i_fertil_ålder"
            )
          }
        >
          <ListItemIcon>
            <WomanIcon />
          </ListItemIcon>
          <Typography variant="inherit">Kvinnor 14 till 44</Typography>
        </MenuItem>

        <MenuItem
          className="StyledMenuItem"
          id={"män_i_vapenför_ålder"}
          value={"män_i_vapenför_ålder"}
          // onClick={props.handleChange}
          selected={selectedIndex === 3}
          onClick={event =>
            handleListItemClick(
              event,
              3,
              props.handleChange,
              "män_i_vapenför_ålder"
            )
          }
        >
          <ListItemIcon>
            <MaleIcon />
          </ListItemIcon>
          <Typography variant="inherit">Män 14 till 44</Typography>
        </MenuItem>

        <MenuItem
          className="StyledMenuItem"
          id={"pensionärer"}
          selected={selectedIndex === 4}
          onClick={event =>
            handleListItemClick(event, 4, props.handleChange, "pensionärer")
          }
        >
          <ListItemIcon>
            <RetiredIcon />
          </ListItemIcon>
          <Typography variant="inherit">Pensionärer</Typography>
        </MenuItem>
      </MenuList>
    </FormControl>
  )
}
