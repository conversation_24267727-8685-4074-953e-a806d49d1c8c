import React from "react"

import "./components.css"

export default function SimpleLegend(props) {
  let colorFunction = props.coloring
  let values = props.values
  let title = props.title
  let numeric = props.numeric

  let LegendLine = function statelessFunctionComponentClass(value) {
    if (numeric) {
      if (value < values[values.length - 1]) {
        return (
          <React.Fragment>
            <i
              style={{
                background: colorFunction(value - 5),
              }}
            ></i>
            <b className="legendText"> &lt;{value}%</b>
            <br></br>
          </React.Fragment>
        )
      } else {
        return (
          <React.Fragment>
            <i
              style={{
                background: colorFunction(value - 5),
              }}
            ></i>
            <b className="legendText"> &lt;{value}%</b>
            <br></br>
            <i
              style={{
                background: colorFunction(value),
              }}
            ></i>
            <b className="legendText"> &gt;{value}%</b>
            <br></br>
          </React.Fragment>
        )
      }
    } else {
      return (
        <React.Fragment>
          <i
            style={{
              background: colorFunction(value),
            }}
          ></i>
          <b className="legendText"> {value}</b>
          <br></br>
        </React.Fragment>
      )
    }
  }

  let CreateLegend = function statelessFunctionComponentClass(values) {
    return values.map(LegendLine)
  }

  return (
    <div id="legendTop">
      <div className="legend">
        <h3
          style={{
            marginTop: 0,
            marginBottom: 0,
            // textAlign: 'right',
            // alignContent: 'right',
            color: "black",
            display: "inline-block",
            width: "max-content",
          }}
        >
          {title.split(" ")[0] ? title.split(" ")[0] : ""}
          <br></br>
          {title.split(" ")[1] ? title.split(" ")[1] : ""}
        </h3>
        <div className="legendTextArea">
          <div className="legendLines">{CreateLegend(values)}</div>
        </div>
      </div>
      {/* <h1 onClick={props.clickMe} id="sourceText">
        Info
      </h1> */}
    </div>
  )
}
