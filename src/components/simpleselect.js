import React from "react"
import { makeStyles } from "@mui/material/styles"
import InputLabel from "@mui/material/InputLabel"
import MenuItem from "@mui/material/MenuItem"
import FormHelperText from "@mui/material/FormHelperText"
import FormControl from "@mui/material/FormControl"
import Select from "@mui/material/Select"

//https://material-ui.com/components/selects/

const useStyles = makeStyles(theme => ({
  formControl: {
    margin: theme.spacing(1),
    minWidth: 300,
  },
  selectEmpty: {
    marginTop: theme.spacing(2),
  },
  defaultMenuOption: {
    fontSize: "small",
  },
}))

export default function SimpleSelect(props) {
  const classes = useStyles()
  // const [age, setAge] = React.useState('')

  // const handleChange = (event) => {
  //     setAge(event.target.value)
  // }

  return (
    <div>
      <FormControl variant="filled" className={classes.formControl}>
        <InputLabel
          className="defaultMenuOption"
          id="demo-simple-select-filled-label"
        >
          Ålderskategori
        </InputLabel>
        <Select
          // labelId="demo-simple-select-filled-label"
          id="demo-simple-select-filled"
          value={props.age}
          onChange={props.handleChange}
        >
          <MenuItem value={"alla"}>Alla</MenuItem>
          <MenuItem value={"barn"}>Barn under 14 år</MenuItem>
          <MenuItem value={"kvinnor_i_fertil_ålder"}>
            kvinnor 14 till 44 år
          </MenuItem>
          <MenuItem value={"män_i_vapenför_ålder"}>män 14 till 44 år</MenuItem>
          <MenuItem value={"pensionärer"}>Pensionärer</MenuItem>
        </Select>
      </FormControl>
    </div>
  )
}
