// src/pages/map.js
import React, { useState, useEffect, useRef } from "react"
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>eoJSO<PERSON> } from "react-leaflet"
import { useIsMounted } from "usehooks-ts"
import Seo from "../components/seo"
// import svenskakommuner from "../../data/sverige-kommuner-with-population-data_extrapolated.json"
import svenskakommuner from "../../data/sverige-kommuner-with-population-data_new.json"
import Slider from "@mui/material/Slider"
import SimplePopup from "../components/simplePopup"
import SimpleLegend from "../components/simpleLegend"
import SimpleDemographicsMenu from "../components/SimpleDemographicsMenu"
import SimpleAreaChart from "../components/stackedArea"

import "./map.css"

const maxDataYear = 2024
const maxYear = 2024
const marks = []
for (let i = 2002; i < maxYear; i = i + 4) {
  marks.push({ value: i, label: i.toString() })
}
function valuetext(value) {
  return `${value}°C`
}

// function percentageColor(percentage) {
//   if (percentage >= 50) return "#000000" // Black
//   else if (percentage >= 45) return "#800026"
//   else if (percentage >= 40) return "#BD0026"
//   else if (percentage >= 35) return "#E31A1C"
//   else if (percentage >= 30) return "#FC4E2A"
//   else if (percentage >= 25) return "#FD8D3C"
//   else if (percentage >= 20) return "#FEB24C"
//   else if (percentage >= 15) return "#FED976"
//   else if (percentage >= 10) return "#ffeda0"
//   else return "#FFFFFF" // White
// }

// function percentageColor(percentage) {
//   if (percentage >= 50) return "#000000" // Black (extreme danger)
//   else if (percentage >= 45) return "#660000" // Very dark red (severe danger)
//   else if (percentage >= 40) return "#990000" // Dark red (high danger)
//   else if (percentage >= 35) return "#CC0000" // Medium red (danger)
//   else if (percentage >= 30) return "#FF0000" // Red (warning)
//   else if (percentage >= 25) return "#FF9900" // Orange (caution)
//   else if (percentage >= 20) return "#FFCC00" // Amber (moderate caution)
//   else if (percentage >= 15) return "#E6E683" // Softer yellow (mild caution)
//   else if (percentage >= 10)
//     return "#CCFF66" // Lighter yellow-green (low caution)
//   else return "#CCFFCC" // Light green (safe)
// }

function percentageColor(percentage) {
  return percentage > 49
    ? "#000000"
    : percentage > 44
    ? "#800026"
    : percentage > 39
    ? "#BD0026"
    : percentage > 34
    ? "#E31A1C"
    : percentage > 29
    ? "#FC4E2A"
    : percentage > 24
    ? "#FD8D3C"
    : percentage > 19
    ? "#FEB24C"
    : percentage > 14
    ? "#FED976"
    : percentage > 9
    ? "#ffeda0"
    : "#FFFFFF"
}

function getColor(befolkning, year, category) {
  let index = year - 2002
  // Give kommun ID number as argument
  // Fetch härkomst % from csv
  // Fetch name from kommunlankod csv
  // console.log(year)
  // console.log(category)
  // console.log(befolkning['svenska_alla'])
  let percentage = 0
  if (
    category === "alla" ||
    category === "barn" ||
    category === "pensionärer" ||
    category === "kvinnor_i_fertil_ålder" ||
    category === "män_i_vapenför_ålder"
  ) {
    if (year >= 2002 || year <= maxYear) {
      percentage =
        (100 * befolkning["utländska_" + category][index]) /
        (befolkning["svenska_" + category][index] +
          befolkning["utländska_" + category][index])
    }
  } else {
    percentage =
      (100 * befolkning["utländska_alla" + category][index]) /
      (befolkning["svenska_alla" + category][index] +
        befolkning["utländska_alla" + category][index])
  }

  return percentageColor(percentage)
}

function polystyle(features, year, age) {
  return {
    fillColor: getColor(features.properties.befolkning, year, age),
    weight: 0.5,
    opacity: 1,
    color: "white", //Outline color
    fillOpacity: 0.7,
  }
}

function highlightFeature(e) {
  var layer = e.target
  layer.openPopup()
  layer.setStyle({
    weight: 5,
    color: "#666",
    dashArray: "",
    fillOpacity: 0.7,
  })
}

function resetHighlight(e) {
  var layer = e.target
  layer.closePopup()
  layer.setStyle({
    weight: 0.5,
    opacity: 1,
    color: "white", //Outline color
    fillOpacity: 0.7,
  })
}

function NewMapPage() {
  // const [map, setMap] = useState(null)
  const [internalYear, setYear] = useState(maxDataYear)
  const [visible, setVisible] = useState(false)
  const [chartVisible, setChartState] = useState(false)
  const [ageCategory, setAgeCategory] = React.useState("alla")
  const [yearText, setYearText] = React.useState(maxDataYear.toString())
  const [kommunFeature, setkommunFeature] = React.useState("")
  const [selectedKommun, setselectedKommun] = React.useState("")
  const [isAutoAdvancing, setIsAutoAdvancing] = useState(false)
  const intervalRef = useRef(null)
  const timeoutRef = useRef(null)

  function setChartVisible(e) {
    let feature = e.target.feature
    // console.log('clicked ' + e.target.feature.properties.kom_namn)
    setselectedKommun(e.target.feature.properties.kom_namn)
    setkommunFeature(feature.properties.befolkning)
    setChartState(true)
    // Pause auto-advance when chart opens
    stopAutoAdvance()
    console.log("chart visible " + chartVisible)
  }

  function setchartInvisible() {
    setChartState(false)
    console.log("chart visible " + chartVisible)
  }

  // Global flag to track touch usage
  let globalTouchUsed = false

  function onEach(feature, layer) {
    if (feature.properties.kom_namn) {
      layer.bindPopup("<h3>" + feature.properties.kom_namn + "</h3>")
    }

    layer.on({
      touchstart: e => {
        globalTouchUsed = true
        // Open chart immediately on touch start
        setChartVisible(e)
        // More aggressive event prevention
        if (e.originalEvent) {
          e.originalEvent.preventDefault()
          e.originalEvent.stopImmediatePropagation()
        }
        e.preventDefault()
        e.stopPropagation()
        return false
      },
      touchend: e => {
        // Reset touch flag after a delay
        setTimeout(() => {
          globalTouchUsed = false
        }, 300)
        if (e.originalEvent) {
          e.originalEvent.preventDefault()
          e.originalEvent.stopImmediatePropagation()
        }
        e.preventDefault()
        e.stopPropagation()
        return false
      },
      click: e => {
        // Only handle click if touch wasn't used
        if (!globalTouchUsed) {
          setChartVisible(e)
        } else {
          e.preventDefault()
          e.stopPropagation()
          return false
        }
      },
      mouseover: e => {
        // Only highlight if touch wasn't used recently
        if (!globalTouchUsed) {
          highlightFeature(e)
        }
      },
      mouseout: e => {
        // Only remove highlight if touch wasn't used recently
        if (!globalTouchUsed) {
          resetHighlight(e)
        }
      },
    })
  }

  const toggleOverlay = () => {
    setVisible(!visible)
    console.log("Clicked legend area " + visible.toString())
  }

  const partialPolystyle = features => {
    return polystyle(features, internalYear, ageCategory)
  }

  const handleSelectorChange = ageString => {
    setAgeCategory(ageString)
  }

  // Auto-advance functionality
  const startAutoAdvance = () => {
    if (intervalRef.current) return // Already running

    intervalRef.current = setInterval(() => {
      setYear(currentYear => {
        const nextYear = currentYear + 1
        if (nextYear > maxYear) {
          // Reset to start when reaching the end
          return 2002
        }

        // Update year text
        let yearText = ""
        if (nextYear <= maxDataYear) {
          yearText = nextYear
        } else {
          yearText = nextYear + " (projektion)"
        }
        setYearText(yearText)

        return nextYear
      })
    }, 1000) // 1 second

    setIsAutoAdvancing(true)
  }

  const stopAutoAdvance = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
    }
    setIsAutoAdvancing(false)
  }

  const handelYearChange = (_, value) => {
    // Stop auto-advance when user interacts with slider
    stopAutoAdvance()

    setYear(value)
    let yearText = ""
    if (value <= maxDataYear) {
      yearText = value
    } else {
      yearText = value + " (projektion)"
    }
    setYearText(yearText)
  }

  // Start auto-advance 10 seconds after page load
  useEffect(() => {
    timeoutRef.current = setTimeout(() => {
      startAutoAdvance()
    }, 3000) // 3 seconds

    // Cleanup on unmount
    return () => {
      stopAutoAdvance()
    }
  }, [])

  return (
    <div className="topContainer">
      <Seo title="Demografi.nu" />
      {useIsMounted && (
        <MapContainer
          id="MapContainer"
          center={[63.1792, 21]}
          zoom={4.5}
          minZoom={4}
          zoomSnap={0.25}
          // whenCreated={setMap}
        >
          <TileLayer
            attribution='&copy; <a href="http://osm.org/copyright">OpenStreetMap</a> contributors'
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          />
          <GeoJSON
            key={internalYear.toString() + ageCategory}
            data={svenskakommuner}
            style={partialPolystyle}
            onEachFeature={onEach}
          ></GeoJSON>
        </MapContainer>
      )}
      {useIsMounted && chartVisible && (
        <SimpleAreaChart
          data={kommunFeature}
          category={ageCategory}
          handleClose={setchartInvisible}
          text={selectedKommun}
        ></SimpleAreaChart>
      )}
      {useIsMounted && visible && (
        <SimplePopup
          content={
            <>
              <p>
                Befolkningsdata är hämtat från{" "}
                <a href="http://www.statistikdatabasen.scb.se/pxweb/sv/ssd/START__BE__BE0101__BE0101Q/UtlSvBakgFin/">
                  SCB
                </a>
                .<br></br> Av utländskt ursprung räknas de som är födda
                utomlands eller födda inrikes med en eller två utländska
                föräldrar.<br></br>
                En linjär extrapolering baserad på de senaste tio årens
                förändring har använts för årtal efter 2023.
              </p>
            </>
          }
          handleClose={toggleOverlay}
        />
      )}
      {useIsMounted && (
        <div className="SimpleMenu">
          <div className="MenuParent">
            <SimpleDemographicsMenu
              handleChange={handleSelectorChange}
              age={ageCategory}
            ></SimpleDemographicsMenu>
            <SimpleLegend
              coloring={percentageColor}
              clickMe={toggleOverlay}
              values={[10, 15, 20, 25, 30, 35, 40, 45, 50]}
              title={"Utländskt ursprung"}
              numeric={true}
            />
          </div>
        </div>
      )}

      {useIsMounted && (
        <div className="Slider">
          <h1 id="SliderText">{yearText}</h1>
          <Slider
            value={internalYear}
            getAriaValueText={valuetext}
            aria-labelledby="discrete-slider-custom"
            step={1}
            valueLabelDisplay="auto"
            marks={marks}
            min={2002}
            max={maxYear}
            onChange={handelYearChange}
          />
        </div>
      )}
    </div>
  )
}

export default NewMapPage
