/* * {
    border: 1px solid red;
} */
/* .test {
  border: 1px solid red;
} */
#topContainer {
  /* position: sticky; */
  margin-top: 0;
  margin-bottom: 0;
  padding-bottom: 0;
  height: 99vh;
  width: 99vw;
  /* margin-right: 1vw; */
  background-color: rgba(170, 168, 168, 0.171);
}
#MapContainer {
  /* background-color: #282c34;
    color: white;
    padding: 40px;
    font-family: Arial;
    text-align: center; */
  height: 99vh;
  width: 99vw;
  top: 0;
  left: 0;
  z-index: 100;

  position: absolute;
}
.Slider {
  width: 95vw;
  position: absolute;
  bottom: 0;
  left: 0;
  padding-left: 5vw;
  padding-right: 5vw;
  z-index: 110;
}
#SliderText {
  text-align: center;
  color: blue;
  margin-top: 0;
  margin-bottom: 0;
  color: #fff;
  -webkit-text-stroke-width: 2px;
  -webkit-text-stroke-color: #282828;
}
#infoBox {
  text-align: center;
  color: blue;
  margin-top: 0;
  margin-bottom: 0;
}
.simpleSelect {
  position: absolute;
  top: 0;
  right: 0;
  padding-left: 5vw;
  padding-right: 0vw;
  padding-top: 5vw;
  z-index: 110;
}

.SimpleMenu {
  position: absolute;
  top: 0;
  right: 0;
  padding-left: 5vw;
  padding-right: 0vw;
  padding-top: 5vw;
  align-items: right;
  /* border: 1px solid red; */

  z-index: 2000; /* Higher than chart to remain clickable */
}

.chartPopup {
  /* border: 1px solid red; */
  position: fixed; /* Changed to fixed to ensure it's on top */
  z-index: 1500; /* Higher than SimpleMenu to appear on top */
  width: 70vw;
  padding-left: 0;
  padding-right: 0;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%); /* Center the chart */
}

/* On mobile screens, allow clicks to pass through to SimpleMenu */
@media (max-width: 640px) {
  .chartPopup {
    pointer-events: none; /* Chart background doesn't capture clicks */
  }

  .chartPopup * {
    pointer-events: auto; /* But chart content still works */
  }
}

@media (min-width: 640px) {
  .chartPopup {
    /* border: 1px solid red;
    position: relative; */
    width: 60vw;
    padding-left: 0;
    padding-right: 0;
    /* z-index: 1000; */
  }
}

/* Chart popup box - the actual class used by SimpleAreaChart */
.chartPopupBox {
  position: fixed;
  z-index: 500; /* Lower than SimpleMenu so menu stays clickable */
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  /* Full screen overlay to capture clicks everywhere */
  display: flex;
  justify-content: center;
  align-items: center;
}

.chartPopupBox .areaChart {
  width: 70vw;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  /* Prevent clicks on the chart itself from closing */
  pointer-events: none;
  z-index: 1000; /* Higher than overlay but lower than SimpleMenu */
  position: relative;
}

.chartPopupBox .areaChart * {
  /* Re-enable pointer events for chart content */
  pointer-events: auto;
}
