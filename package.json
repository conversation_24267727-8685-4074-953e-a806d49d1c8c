{"name": "gatsby-starter-default", "private": true, "description": "A simple starter to get up and developing quickly with Gatsby", "version": "0.1.0", "author": "<PERSON> <<EMAIL>>", "dependencies": {"@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@mapbox/leaflet-omnivore": "^0.3.4", "@mui/material": "^5.10.17", "color-between": "^1.0.2", "gatsby": "^5.2.0", "gatsby-plugin-image": "^3.2.0", "gatsby-plugin-manifest": "^5.2.0", "gatsby-plugin-react-leaflet": "^4.0.3", "gatsby-plugin-sharp": "^5.2.0", "gatsby-source-filesystem": "^5.2.0", "gatsby-transformer-csv": "^5.2.0", "gatsby-transformer-sharp": "^5.2.0", "highcharts": "^10.3.2", "highcharts-react-official": "^3.1.0", "leaflet": "^1.9.3", "mdi-material-ui": "^7.6.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-leaflet": "^4.2.0", "reactjs-popup": "^2.0.5", "shpjs": "^4.0.4", "tss-react": "^4.4.5", "usehooks-ts": "^2.9.1"}, "devDependencies": {"prettier": "^2.8.0"}, "keywords": ["gatsby"], "license": "0BSD", "scripts": {"build": "gatsby build", "develop": "gatsby develop", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md,css}\"", "start": "gatsby develop", "serve": "gatsby serve", "clean": "gatsby clean", "test": "echo \"Write tests! -> https://gatsby.dev/unit-testing\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/gatsbyjs/gatsby-starter-default"}, "bugs": {"url": "https://github.com/gatsbyjs/gatsby/issues"}}